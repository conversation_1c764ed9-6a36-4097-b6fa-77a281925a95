from pydantic import BaseModel
from pathlib import Path
from typing import List, Optional, Union
import fnmatch

from core.config import get_config


class FileNode(BaseModel):
    id: str
    name: str
    type: str  # 'file' or 'directory'
    path: str
    size: Optional[int] = None
    lastModified: Optional[str] = None
    children: Optional[List['FileNode']] = None
    isExpanded: bool = False

# 工具函数
def get_file_size(file_path: Path) -> int:
    """获取文件大小"""
    try:
        return file_path.stat().st_size
    except:
        return 0

def get_last_modified(file_path: Path) -> str:
    """获取文件最后修改时间"""
    try:
        return file_path.stat().st_mtime.__str__()
    except:
        return ""

def get_file_filter_config():
    """获取文件过滤配置"""
    try:
        config = get_config()
        return {
            'exclude': config.file_filter.exclude,
            'include': config.file_filter.include,
            'max_file_size': config.file_filter.max_file_size
        }
    except Exception:
        # 配置加载失败时使用默认配置
        return {
            'exclude': ['.git', '.svn', '.hg', 'node_modules', '__pycache__', '.pytest_cache',
                       'target', 'build', 'dist', '.next', '.vscode', '.idea', '.DS_Store',
                       '*.pyc', '*.pyo', '*.pyd', '.venv'],
            'include': [".py", ".js", ".ts", ".java", ".cpp", ".c", ".h", ".hpp",
                       ".go", ".rs", ".php", ".rb", ".md"],
            'max_file_size': 1048576  # 1MB
        }


def should_ignore_path(path: Union[Path | str]) -> bool:
    """根据配置文件中的设置判断是否应该忽略某个路径"""
    filter_config = get_file_filter_config()
    exclude_patterns = filter_config['exclude']
    include_extensions = filter_config['include']
    max_file_size = filter_config['max_file_size']

    if isinstance(path, str):
        path = Path(path)
    name = path.name

    # 检查排除模式
    for pattern in exclude_patterns:
        if pattern.startswith('*'):
            # 处理通配符模式，如 *.pyc
            if fnmatch.fnmatch(name, pattern):
                return True
        else:
            # 处理精确匹配或目录名
            if name == pattern or (path.is_dir() and name == pattern):
                return True

    # 如果是文件，检查扩展名和大小
    if path.is_file():
        # 检查文件扩展名是否在包含列表中
        file_extension = path.suffix.lower()
        if include_extensions and file_extension not in include_extensions:
            return True

        # 检查文件大小
        try:
            if path.stat().st_size > max_file_size:
                return True
        except (OSError, PermissionError):
            # 如果无法获取文件大小，默认不忽略
            pass

    return False

def build_file_tree(dir_path: Path, workspace_id: str = "", max_depth: int = 10, current_depth: int = 0) -> List[FileNode]:
    """构建文件树"""
    if current_depth >= max_depth:
        return []

    nodes = []
    try:
        # 自定义排序：目录在前，文件在后，然后按名称字母顺序排序
        def sort_key(item):
            # 目录返回 (0, 名称)，文件返回 (1, 名称)
            return (0 if item.is_dir() else 1, item.name.lower())

        for item in sorted(dir_path.iterdir(), key=sort_key):
            if should_ignore_path(item):
                continue

            node = FileNode(
                id=str(item),
                name=item.name,
                type="directory" if item.is_dir() else "file",
                path=str(item),  # 使用绝对路径
                isExpanded=False
            )

            if item.is_file():
                node.size = get_file_size(item)
                node.lastModified = get_last_modified(item)
            elif item.is_dir():
                # 递归获取子目录
                node.children = build_file_tree(
                    item, workspace_id, max_depth, current_depth + 1
                )

            nodes.append(node)
    except PermissionError:
        pass

    return nodes
