2025-08-13 15:11:30 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:18:57 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:22:22 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:23:01 - utils.logger - INFO - Full path: data/repos/data/repos/ruoyi-vue-pro/.github/ISSUE_TEMPLATE/question.md
2025-08-13 15:32:04 - utils.logger - INFO - Full path: data/repos/data/repos/ruoyi-vue-pro/sql/db2/README.md
2025-08-13 15:32:28 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:32:34 - utils.logger - INFO - Full path: data/repos/ruoyi-vue-pro/script/docker/Docker-HOWTO.md
2025-08-13 15:34:19 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:34:57 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:35:00 - utils.logger - INFO - Start DeepSearch for Query: 功能文件列表
2025-08-13 15:39:08 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:39:29 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:41:46 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:41:56 - utils.logger - INFO - Full path: data/repos/ruoyi-vue-pro/README.md
2025-08-13 15:41:59 - utils.logger - INFO - Start DeepSearch for Query: 功能文件列表
2025-08-13 15:41:59 - utils.logger - ERROR - DeepSearch Failed: 'DeepSearch' object has no attribute 'llm_client'
2025-08-13 15:42:22 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:42:24 - utils.logger - INFO - Start DeepSearch for Query: 功能文件列表
2025-08-13 15:42:28 - utils.logger - INFO - Iteration 1: 功能文件列表
2025-08-13 15:42:28 - utils.logger - INFO - Iteration 1: Generated 2 New Queries: ['如何列出指定目录下所有文件和文件夹', 'Python获取文件目录结构列表代码示例']
2025-08-13 15:42:28 - utils.logger - INFO - Query '如何列出指定目录下所有文件和文件夹' Found 0 Code Snippets
2025-08-13 15:42:28 - utils.logger - INFO - Query 'Python获取文件目录结构列表代码示例' Found 0 Code Snippets
2025-08-13 15:42:28 - utils.logger - INFO - Found 0 Code Snippets, Start Filtering...
2025-08-13 15:42:28 - utils.logger - INFO - Filtered Snippets Deduplicated to 0 Unique Snippets
2025-08-13 15:42:28 - utils.logger - INFO - 新查询未找到相关代码，搜索结束
2025-08-13 15:42:28 - utils.logger - INFO - Merging Code Snippets at File Level...
2025-08-13 15:42:28 - utils.logger - INFO - DeepSearch Completed: 搜索摘要:
- 原始查询: 功能文件列表
- 子查询数量: 0
- 总查询数量: 2
- 找到的代码片段: 0
- 涉及文件数: 0
- 迭代次数: 1

2025-08-13 15:45:06 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:45:25 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:45:30 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:45:43 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:46:37 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:46:39 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:46:43 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:46:48 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:49:28 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:50:59 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:51:01 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:51:03 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:51:12 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:51:13 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:51:16 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:52:04 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:52:16 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:52:23 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:52:24 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:52:42 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:52:54 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:53:02 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:53:23 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:53:35 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:53:46 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:53:54 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:54:06 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:54:10 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:54:14 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:54:15 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:54:21 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:54:39 - utils.logger - INFO - Start DeepSearch for Query: 连接池配置在哪里
2025-08-13 15:54:41 - utils.logger - INFO - Iteration 1: 连接池配置在哪里
2025-08-13 15:54:41 - utils.logger - INFO - Iteration 1: Generated 2 New Queries: ['连接池配置代码示例', 'jdbc连接池配置参数说明']
2025-08-13 15:54:42 - utils.logger - INFO - Query '连接池配置代码示例' Found 0 Code Snippets
2025-08-13 15:54:42 - utils.logger - INFO - Query 'jdbc连接池配置参数说明' Found 0 Code Snippets
2025-08-13 15:54:42 - utils.logger - INFO - Found 0 Code Snippets, Start Filtering...
2025-08-13 15:54:42 - utils.logger - INFO - Filtered Snippets Deduplicated to 0 Unique Snippets
2025-08-13 15:54:42 - utils.logger - INFO - 新查询未找到相关代码，搜索结束
2025-08-13 15:54:42 - utils.logger - INFO - Merging Code Snippets at File Level...
2025-08-13 15:54:42 - utils.logger - INFO - DeepSearch Completed: 搜索摘要:
- 原始查询: 连接池配置在哪里
- 子查询数量: 0
- 总查询数量: 2
- 找到的代码片段: 0
- 涉及文件数: 0
- 迭代次数: 1

2025-08-13 15:56:05 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:56:07 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:56:13 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:56:15 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:56:22 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:56:24 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:56:33 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:56:35 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:56:40 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:56:47 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:56:53 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:56:59 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:57:11 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:57:13 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:57:16 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:57:25 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:58:06 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:58:09 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:58:13 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:58:31 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:58:32 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:58:37 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:58:39 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:58:43 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:58:47 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:58:49 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:58:51 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:58:53 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:58:57 - utils.logger - INFO - Start DeepSearch for Query: 连接池配置在哪里
2025-08-13 15:58:59 - utils.logger - INFO - Iteration 1: 连接池配置在哪里
2025-08-13 15:58:59 - utils.logger - INFO - Iteration 1: Generated 2 New Queries: ['数据库连接池配置', 'jdbc连接池配置']
2025-08-13 15:59:00 - utils.logger - INFO - Query '数据库连接池配置' Found 0 Code Snippets
2025-08-13 15:59:00 - utils.logger - INFO - Query 'jdbc连接池配置' Found 0 Code Snippets
2025-08-13 15:59:00 - utils.logger - INFO - Found 0 Code Snippets, Start Filtering...
2025-08-13 15:59:00 - utils.logger - INFO - Filtered Snippets Deduplicated to 0 Unique Snippets
2025-08-13 15:59:00 - utils.logger - INFO - 新查询未找到相关代码，搜索结束
2025-08-13 15:59:00 - utils.logger - INFO - Merging Code Snippets at File Level...
2025-08-13 15:59:00 - utils.logger - INFO - DeepSearch Completed: 搜索摘要:
- 原始查询: 连接池配置在哪里
- 子查询数量: 0
- 总查询数量: 2
- 找到的代码片段: 0
- 涉及文件数: 0
- 迭代次数: 1

2025-08-13 15:59:29 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 15:59:31 - utils.logger - INFO - Start DeepSearch for Query: 连接池配置在哪里
2025-08-13 15:59:34 - utils.logger - INFO - Iteration 1: 连接池配置在哪里
2025-08-13 15:59:34 - utils.logger - INFO - Iteration 1: Generated 2 New Queries: ['连接池配置类', 'datasource配置']
2025-08-13 15:59:35 - utils.logger - INFO - Query '连接池配置类' Found 0 Code Snippets
2025-08-13 15:59:35 - utils.logger - INFO - Query 'datasource配置' Found 0 Code Snippets
2025-08-13 15:59:35 - utils.logger - INFO - Found 0 Code Snippets, Start Filtering...
2025-08-13 15:59:35 - utils.logger - INFO - Filtered Snippets Deduplicated to 0 Unique Snippets
2025-08-13 15:59:35 - utils.logger - INFO - 新查询未找到相关代码，搜索结束
2025-08-13 15:59:35 - utils.logger - INFO - Merging Code Snippets at File Level...
2025-08-13 15:59:35 - utils.logger - INFO - DeepSearch Completed: 搜索摘要:
- 原始查询: 连接池配置在哪里
- 子查询数量: 0
- 总查询数量: 2
- 找到的代码片段: 0
- 涉及文件数: 0
- 迭代次数: 1

2025-08-13 16:00:04 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:00:05 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:00:15 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:00:21 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:00:25 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:00:29 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:00:32 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:00:37 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:00:45 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:00:52 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:01:00 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:01:03 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:01:06 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:01:12 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:01:16 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:01:18 - utils.logger - INFO - Start DeepSearch for Query: 连接池配置在哪里
2025-08-13 16:01:21 - utils.logger - INFO - Iteration 1: 连接池配置在哪里
2025-08-13 16:01:21 - utils.logger - INFO - Iteration 1: Generated 2 New Queries: ['数据库连接池配置文件', 'spring boot datasource pool config']
2025-08-13 16:01:21 - utils.logger - INFO - Query '数据库连接池配置文件' Found 0 Code Snippets
2025-08-13 16:01:21 - utils.logger - INFO - Query 'spring boot datasource pool config' Found 0 Code Snippets
2025-08-13 16:01:21 - utils.logger - INFO - Found 0 Code Snippets, Start Filtering...
2025-08-13 16:01:21 - utils.logger - INFO - Filtered Snippets Deduplicated to 0 Unique Snippets
2025-08-13 16:01:21 - utils.logger - INFO - 新查询未找到相关代码，搜索结束
2025-08-13 16:01:21 - utils.logger - INFO - Merging Code Snippets at File Level...
2025-08-13 16:01:21 - utils.logger - INFO - DeepSearch Completed: 搜索摘要:
- 原始查询: 连接池配置在哪里
- 子查询数量: 0
- 总查询数量: 2
- 找到的代码片段: 0
- 涉及文件数: 0
- 迭代次数: 1

2025-08-13 16:02:22 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:02:30 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:02:37 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:02:41 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:02:43 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:02:45 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:02:47 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:02:48 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:02:51 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:03:01 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:03:08 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:03:13 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:03:15 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:03:16 - utils.logger - INFO - Start DeepSearch for Query: 连接池配置在哪里
2025-08-13 16:03:20 - utils.logger - INFO - Iteration 1: 连接池配置在哪里
2025-08-13 16:03:20 - utils.logger - INFO - Iteration 1: Generated 2 New Queries: ['连接池配置文件路径', 'DataSource配置属性']
2025-08-13 16:03:21 - utils.logger - INFO - Query '连接池配置文件路径' Found 0 Code Snippets
2025-08-13 16:03:21 - utils.logger - INFO - Query 'DataSource配置属性' Found 0 Code Snippets
2025-08-13 16:03:21 - utils.logger - INFO - Found 0 Code Snippets, Start Filtering...
2025-08-13 16:03:21 - utils.logger - INFO - Filtered Snippets Deduplicated to 0 Unique Snippets
2025-08-13 16:03:21 - utils.logger - INFO - 新查询未找到相关代码，搜索结束
2025-08-13 16:03:21 - utils.logger - INFO - Merging Code Snippets at File Level...
2025-08-13 16:03:21 - utils.logger - INFO - DeepSearch Completed: 搜索摘要:
- 原始查询: 连接池配置在哪里
- 子查询数量: 0
- 总查询数量: 2
- 找到的代码片段: 0
- 涉及文件数: 0
- 迭代次数: 1

2025-08-13 16:04:17 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:05:08 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:05:09 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:05:14 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:05:18 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:05:20 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:05:41 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:05:54 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:05:55 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:05:57 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:05:59 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:06:03 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:06:04 - utils.logger - INFO - Start DeepSearch for Query: 连接池配置在哪里
2025-08-13 16:06:06 - utils.logger - INFO - Iteration 1: 连接池配置在哪里
2025-08-13 16:06:06 - utils.logger - INFO - Iteration 1: Generated 2 New Queries: ['connection pool configuration', 'datasource config']
2025-08-13 16:06:07 - utils.logger - INFO - Query 'datasource config' Found 0 Code Snippets
2025-08-13 16:06:07 - utils.logger - INFO - Query 'connection pool configuration' Found 0 Code Snippets
2025-08-13 16:06:07 - utils.logger - INFO - Found 0 Code Snippets, Start Filtering...
2025-08-13 16:06:07 - utils.logger - INFO - Filtered Snippets Deduplicated to 0 Unique Snippets
2025-08-13 16:06:07 - utils.logger - INFO - 新查询未找到相关代码，搜索结束
2025-08-13 16:06:07 - utils.logger - INFO - Merging Code Snippets at File Level...
2025-08-13 16:06:07 - utils.logger - INFO - DeepSearch Completed: 搜索摘要:
- 原始查询: 连接池配置在哪里
- 子查询数量: 0
- 总查询数量: 2
- 找到的代码片段: 0
- 涉及文件数: 0
- 迭代次数: 1

2025-08-13 16:06:29 - utils.logger - INFO - Full path: data/repos/ruoyi-vue-pro/sql/db2/README.md
2025-08-13 16:06:39 - utils.logger - INFO - Start DeepSearch for Query: 表结构与数据
2025-08-13 16:06:43 - utils.logger - INFO - Iteration 1: 表结构与数据
2025-08-13 16:06:43 - utils.logger - INFO - Iteration 1: Generated 2 New Queries: ['table schema', 'database structure']
2025-08-13 16:06:43 - utils.logger - INFO - Query 'table schema' Found 0 Code Snippets
2025-08-13 16:06:43 - utils.logger - INFO - Query 'database structure' Found 0 Code Snippets
2025-08-13 16:06:43 - utils.logger - INFO - Found 0 Code Snippets, Start Filtering...
2025-08-13 16:06:43 - utils.logger - INFO - Filtered Snippets Deduplicated to 0 Unique Snippets
2025-08-13 16:06:43 - utils.logger - INFO - 新查询未找到相关代码，搜索结束
2025-08-13 16:06:43 - utils.logger - INFO - Merging Code Snippets at File Level...
2025-08-13 16:06:43 - utils.logger - INFO - DeepSearch Completed: 搜索摘要:
- 原始查询: 表结构与数据
- 子查询数量: 0
- 总查询数量: 2
- 找到的代码片段: 0
- 涉及文件数: 0
- 迭代次数: 1

2025-08-13 16:07:01 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:07:04 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:07:31 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:07:43 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:07:50 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:07:51 - utils.logger - INFO - Start DeepSearch for Query: 表结构与数据
2025-08-13 16:07:54 - utils.logger - INFO - Iteration 1: 表结构与数据
2025-08-13 16:07:54 - utils.logger - INFO - Iteration 1: Generated 2 New Queries: ['table schema', 'database structure']
2025-08-13 16:07:54 - utils.logger - INFO - Query 'database structure' Found 0 Code Snippets
2025-08-13 16:07:54 - utils.logger - INFO - Query 'table schema' Found 0 Code Snippets
2025-08-13 16:07:54 - utils.logger - INFO - Found 0 Code Snippets, Start Filtering...
2025-08-13 16:07:54 - utils.logger - INFO - Filtered Snippets Deduplicated to 0 Unique Snippets
2025-08-13 16:07:54 - utils.logger - INFO - 新查询未找到相关代码，搜索结束
2025-08-13 16:07:54 - utils.logger - INFO - Merging Code Snippets at File Level...
2025-08-13 16:07:54 - utils.logger - INFO - DeepSearch Completed: 搜索摘要:
- 原始查询: 表结构与数据
- 子查询数量: 0
- 总查询数量: 2
- 找到的代码片段: 0
- 涉及文件数: 0
- 迭代次数: 1

2025-08-13 16:08:10 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:08:11 - utils.logger - INFO - Start DeepSearch for Query: 表结构与数据
2025-08-13 16:08:13 - utils.logger - INFO - Iteration 1: 表结构与数据
2025-08-13 16:08:13 - utils.logger - INFO - Iteration 1: Generated 2 New Queries: ['Table', 'Schema']
2025-08-13 16:08:14 - utils.logger - ERROR - Grep Failed: 'GrepSearchTool' object has no attribute '_is_valid_file'
2025-08-13 16:08:14 - utils.logger - ERROR - Grep Failed: 'GrepSearchTool' object has no attribute '_is_valid_file'
2025-08-13 16:08:14 - utils.logger - INFO - Query 'Table' Found 0 Code Snippets
2025-08-13 16:08:14 - utils.logger - INFO - Query 'Schema' Found 0 Code Snippets
2025-08-13 16:08:14 - utils.logger - INFO - Found 0 Code Snippets, Start Filtering...
2025-08-13 16:08:14 - utils.logger - INFO - Filtered Snippets Deduplicated to 0 Unique Snippets
2025-08-13 16:08:14 - utils.logger - INFO - 新查询未找到相关代码，搜索结束
2025-08-13 16:08:14 - utils.logger - INFO - Merging Code Snippets at File Level...
2025-08-13 16:08:14 - utils.logger - INFO - DeepSearch Completed: 搜索摘要:
- 原始查询: 表结构与数据
- 子查询数量: 0
- 总查询数量: 2
- 找到的代码片段: 0
- 涉及文件数: 0
- 迭代次数: 1

2025-08-13 16:08:44 - utils.logger - INFO - Full path: data/repos/ruoyi-vue-pro/README.md
2025-08-13 16:08:57 - utils.logger - INFO - Start DeepSearch for Query: HTML
2025-08-13 16:08:59 - utils.logger - INFO - Iteration 1: HTML
2025-08-13 16:08:59 - utils.logger - INFO - Iteration 1: Generated 2 New Queries: ['html', 'tag']
2025-08-13 16:09:00 - utils.logger - ERROR - Grep Failed: 'GrepSearchTool' object has no attribute '_is_valid_file'
2025-08-13 16:09:00 - utils.logger - ERROR - Grep Failed: 'GrepSearchTool' object has no attribute '_is_valid_file'
2025-08-13 16:09:00 - utils.logger - INFO - Query 'tag' Found 0 Code Snippets
2025-08-13 16:09:00 - utils.logger - INFO - Query 'html' Found 0 Code Snippets
2025-08-13 16:09:00 - utils.logger - INFO - Found 0 Code Snippets, Start Filtering...
2025-08-13 16:09:00 - utils.logger - INFO - Filtered Snippets Deduplicated to 0 Unique Snippets
2025-08-13 16:09:00 - utils.logger - INFO - 新查询未找到相关代码，搜索结束
2025-08-13 16:09:00 - utils.logger - INFO - Merging Code Snippets at File Level...
2025-08-13 16:09:00 - utils.logger - INFO - DeepSearch Completed: 搜索摘要:
- 原始查询: HTML
- 子查询数量: 0
- 总查询数量: 2
- 找到的代码片段: 0
- 涉及文件数: 0
- 迭代次数: 1

2025-08-13 16:09:43 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:09:46 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:09:49 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:09:54 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:09:59 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:10:01 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:10:03 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:10:05 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:10:14 - utils.logger - INFO - Start DeepSearch for Query: HTML
2025-08-13 16:10:16 - utils.logger - INFO - Iteration 1: HTML
2025-08-13 16:10:16 - utils.logger - INFO - Iteration 1: Generated 2 New Queries: ['html', 'structure']
2025-08-13 16:10:17 - utils.logger - ERROR - Grep Failed: 'str' object has no attribute 'name'
2025-08-13 16:10:17 - utils.logger - ERROR - Grep Failed: 'str' object has no attribute 'name'
2025-08-13 16:10:17 - utils.logger - INFO - Query 'structure' Found 0 Code Snippets
2025-08-13 16:10:17 - utils.logger - INFO - Query 'html' Found 0 Code Snippets
2025-08-13 16:10:17 - utils.logger - INFO - Found 0 Code Snippets, Start Filtering...
2025-08-13 16:10:17 - utils.logger - INFO - Filtered Snippets Deduplicated to 0 Unique Snippets
2025-08-13 16:10:17 - utils.logger - INFO - 新查询未找到相关代码，搜索结束
2025-08-13 16:10:17 - utils.logger - INFO - Merging Code Snippets at File Level...
2025-08-13 16:10:17 - utils.logger - INFO - DeepSearch Completed: 搜索摘要:
- 原始查询: HTML
- 子查询数量: 0
- 总查询数量: 2
- 找到的代码片段: 0
- 涉及文件数: 0
- 迭代次数: 1

2025-08-13 16:11:48 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:11:56 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:12:02 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:12:10 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:12:13 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:12:18 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:12:19 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:12:22 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:12:25 - utils.logger - INFO - Start DeepSearch for Query: HTML
2025-08-13 16:12:28 - utils.logger - INFO - Iteration 1: HTML
2025-08-13 16:12:28 - utils.logger - INFO - Iteration 1: Generated 2 New Queries: ['HTML', 'DOCTYPE']
2025-08-13 16:12:29 - utils.logger - INFO - Query 'DOCTYPE' Found 3 Code Snippets
2025-08-13 16:12:29 - utils.logger - INFO - Query 'HTML' Found 86 Code Snippets
2025-08-13 16:12:29 - utils.logger - INFO - Found 89 Code Snippets, Start Filtering...
2025-08-13 16:12:29 - utils.logger - INFO - Sub Query 'DOCTYPE' Filter Failed: 'CodeSnippet' object has no attribute 'get_full_content'
2025-08-13 16:12:29 - utils.logger - INFO - Sub Query 'HTML' Filter Failed: 'CodeSnippet' object has no attribute 'get_full_content'
2025-08-13 16:12:29 - utils.logger - INFO - Filtered Snippets Deduplicated to 0 Unique Snippets
2025-08-13 16:12:29 - utils.logger - INFO - 新查询未找到相关代码，搜索结束
2025-08-13 16:12:29 - utils.logger - INFO - Merging Code Snippets at File Level...
2025-08-13 16:12:29 - utils.logger - INFO - DeepSearch Completed: 搜索摘要:
- 原始查询: HTML
- 子查询数量: 0
- 总查询数量: 2
- 找到的代码片段: 0
- 涉及文件数: 0
- 迭代次数: 1

2025-08-13 16:12:53 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:12:57 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:13:26 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:13:28 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:13:42 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 16:13:44 - utils.logger - INFO - Start DeepSearch for Query: HTML
2025-08-13 16:13:46 - utils.logger - INFO - Iteration 1: HTML
2025-08-13 16:13:46 - utils.logger - INFO - Iteration 1: Generated 2 New Queries: ['html', 'element']
2025-08-13 16:13:47 - utils.logger - INFO - Query 'html' Found 86 Code Snippets
2025-08-13 16:13:47 - utils.logger - INFO - Query 'element' Found 548 Code Snippets
2025-08-13 16:13:47 - utils.logger - INFO - Found 634 Code Snippets, Start Filtering...
2025-08-13 16:17:14 - utils.logger - INFO -  Sub Query 'html' Filtered 0 Snippets
